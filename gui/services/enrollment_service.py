import os
import uuid
import cv2
import tempfile
from datetime import datetime
from flask import Flask, request, render_template_string, jsonify

# Force CPU-only mode to avoid CUDA issues
os.environ['CUDA_VISIBLE_DEVICES'] = ''
os.environ['DLIB_USE_CUDA'] = '0'

import face_recognition
from facial_recognition_system.face_processing import process_person_images
from facial_recognition_system.database import add_person_to_database


app = Flask(__name__)
UPLOAD_BASE_FOLDER = 'student_uploads'
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg'}

os.makedirs(UPLOAD_BASE_FOLDER, exist_ok=True)
app.config['MAX_CONTENT_LENGTH'] = 64 * 1024 * 1024  # 64 MB

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def check_face_in_image(file_path):
    print(f"DEBUG: Checking face in image: {file_path}")
    try:
        image = cv2.imread(file_path)
        if image is None:
            print(f"DEBUG ERROR: Could not read image file: {file_path}")
            return False, "Could not read image", []

        print(f"DEBUG: Image loaded successfully, original size: {image.shape}")

        # Resize if too large
        height, width = image.shape[:2]
        if max(height, width) > 640:
            scale = 640 / max(height, width)
            new_width = int(width * scale)
            new_height = int(height * scale)
            image = cv2.resize(image, (new_width, new_height))
            print(f"DEBUG: Image resized to: {new_width}x{new_height}")

        # Detect faces
        print("DEBUG: Starting face detection...")
        rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        face_locations = face_recognition.face_locations(rgb_image, model='hog')
        print(f"DEBUG: Face detection completed. Found {len(face_locations)} faces")

        if len(face_locations) == 0:
            print("DEBUG: No faces detected in image")
            return False, "No face", []
        elif len(face_locations) > 1:
            print(f"DEBUG: Multiple faces detected: {len(face_locations)}")
            return False, "Multiple faces", face_locations
        else:
            print("DEBUG: Exactly one face detected - good!")
            return True, "One face", face_locations
    except Exception as e:
        print(f"DEBUG ERROR: Exception in face detection: {e}")
        print(f"DEBUG ERROR: Exception type: {type(e).__name__}")
        return False, "Error", []

@app.route('/check-face', methods=['POST'])
def check_face():
    print("DEBUG: Face check endpoint called")

    if 'image' not in request.files:
        print("DEBUG ERROR: No image file in request")
        return jsonify({'success': False, 'message': 'No image file', 'face_count': 0}), 400

    file = request.files['image']
    print(f"DEBUG: Received file: '{file.filename}'")

    if file.filename == '' or not allowed_file(file.filename):
        print(f"DEBUG ERROR: Invalid file: '{file.filename}'")
        return jsonify({'success': False, 'message': 'Invalid file', 'face_count': 0}), 400

    try:
        with tempfile.NamedTemporaryFile(delete=False, suffix='.jpg') as temp:
            file.save(temp.name)
            temp_path = temp.name
            print(f"DEBUG: Saved temp file: {temp_path}")

        try:
            success, message, face_locations = check_face_in_image(temp_path)
            print(f"DEBUG: Face check result - Success: {success}, Message: '{message}', Faces: {len(face_locations)}")

            return jsonify({
                'success': success,
                'message': message,
                'face_count': len(face_locations),
                'image_id': str(uuid.uuid4())
            })
        finally:
            if os.path.exists(temp_path):
                os.remove(temp_path)
                print(f"DEBUG: Cleaned up temp file: {temp_path}")

    except Exception as e:
        print(f"DEBUG ERROR: Exception in face check endpoint: {e}")
        return jsonify({'success': False, 'message': f'Error: {str(e)}', 'face_count': 0}), 500

# HTML form template with original design in French
HTML_FORM = '''
<!doctype html>
<html>
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Inscription Étudiant</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            background-color: #f5f7fa;
            margin: 0;
            padding: 10px;
            color: #333;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            padding: 20px;
            position: relative;
        }
        h1, h2 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            font-weight: 500;
            display: block;
            margin-bottom: 8px;
            color: #34495e;
        }
        .form-control {
            border-radius: 5px;
            border: 1px solid #ddd;
            padding: 12px 15px;
            width: 100%;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        .form-control:focus {
            border-color: #3498db;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
        }
        .btn {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            width: 100%;
            transition: background-color 0.3s;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        .btn:hover {
            background-color: #2980b9;
        }
        .btn:disabled {
            background-color: #95a5a6;
            cursor: not-allowed;
        }
        .file-upload {
            border: 2px dashed #ddd;
            border-radius: 5px;
            padding: 20px;
            text-align: center;
            margin-bottom: 20px;
            transition: all 0.3s;
            position: relative;
        }
        .file-upload.highlight {
            border-color: #3498db;
            background-color: rgba(52, 152, 219, 0.05);
        }
        .file-upload-label {
            display: block;
            font-weight: 500;
            margin-bottom: 10px;
            color: #34495e;
        }
        .file-upload-input {
            position: absolute;
            left: 0;
            top: 0;
            right: 0;
            bottom: 0;
            opacity: 0;
            cursor: pointer;
            width: 100%;
            height: 100%;
        }
        .file-upload-text {
            color: #7f8c8d;
            margin-bottom: 10px;
        }
        .file-upload-icon {
            font-size: 40px;
            color: #bdc3c7;
            margin-bottom: 10px;
        }
        .preview-container {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 20px;
        }
        .preview-item {
            position: relative;
            width: 100px;
            height: 100px;
            border-radius: 5px;
            overflow: hidden;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            transition: background-color 0.3s ease;
        }
        .preview-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        .preview-remove {
            position: absolute;
            top: 5px;
            right: 5px;
            background: rgba(0,0,0,0.5);
            color: white;
            border: none;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }
        .progress-container {
            margin-top: 20px;
            margin-bottom: 20px;
            display: none;
        }
        .face-check-overlay {
            position: absolute;
            top: 5px;
            right: 30px;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
            z-index: 5;
        }
        .face-check-overlay.success {
            background-color: #2ecc71;
        }
        .face-check-overlay.error {
            background-color: #e74c3c;
        }
        .progress {
            height: 10px;
            border-radius: 5px;
            background-color: #ecf0f1;
            overflow: hidden;
            margin-bottom: 10px;
        }
        .progress-bar {
            height: 100%;
            background-color: #2ecc71;
            width: 0%;
            transition: width 0.3s;
        }
        .progress-text {
            text-align: center;
            font-size: 14px;
            color: #7f8c8d;
        }
        .status-badge {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            background-color: #e8f5e9;
            color: #2e7d32;
            margin-bottom: 20px;
        }
        .status-badge i {
            margin-right: 5px;
        }
        .required-field::after {
            content: "*";
            color: #e74c3c;
            margin-left: 4px;
        }
        .error-message {
            color: #e74c3c;
            font-size: 14px;
            margin-top: 5px;
            display: none;
        }
        @media (max-width: 768px) {
            .container {
                padding: 20px;
            }
            .preview-item {
                width: 80px;
                height: 80px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="status-badge">
            <i class="fas fa-wifi"></i> Connecté
        </div>
        <h2>Formulaire d'Inscription Étudiant</h2>
        <form method="POST" enctype="multipart/form-data" id="uploadForm">
            <div class="form-group">
                <label for="name" class="required-field">Nom</label>
                <input type="text" class="form-control" id="name" name="name" required>
                <div class="error-message" id="nameError">Veuillez saisir le nom</div>
            </div>

            <div class="form-group">
                <label for="familyName" class="required-field">Prénom</label>
                <input type="text" class="form-control" id="familyName" name="family_name" required>
                <div class="error-message" id="familyNameError">Veuillez saisir le prénom</div>
            </div>

            <div class="form-group">
                <label for="dob" class="required-field">Date de naissance</label>
                <input type="date" class="form-control" id="dob" name="dob" required>
                <div class="error-message" id="dobError">Veuillez saisir la date de naissance</div>
            </div>

            <div class="form-group">
                <label class="required-field">Photos de visage</label>
                <div class="file-upload" id="dropArea">
                    <i class="fas fa-cloud-upload-alt file-upload-icon"></i>
                    <p class="file-upload-text">Glissez-déposez vos images ici ou cliquez pour parcourir</p>
                    <p class="file-upload-text">Veuillez télécharger au moins une photo claire de votre visage (maximum 10)</p>
                    <input type="file" class="file-upload-input" id="fileInput" name="files[]" accept="image/*" multiple>
                </div>
                <div class="error-message" id="fileError">Veuillez télécharger au moins une image</div>

                <div class="preview-container" id="previewContainer"></div>

                <div class="progress-container" id="progressContainer">
                    <div class="progress">
                        <div class="progress-bar" id="progressBar"></div>
                    </div>
                    <div class="progress-text" id="progressText">Téléchargement... 0%</div>
                </div>
            </div>

            <button type="submit" class="btn" id="submitBtn">
                <i class="fas fa-paper-plane"></i>Soumettre
            </button>
        </form>
    </div>

    <script>
        // Fonctionnalité de prévisualisation de téléchargement de fichiers
        const dropArea = document.getElementById('dropArea');
        const fileInput = document.getElementById('fileInput');
        const previewContainer = document.getElementById('previewContainer');
        const progressContainer = document.getElementById('progressContainer');
        const progressBar = document.getElementById('progressBar');
        const progressText = document.getElementById('progressText');
        const submitBtn = document.getElementById('submitBtn');
        const form = document.getElementById('uploadForm');

        // Empêcher les comportements de glisser par défaut
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            dropArea.addEventListener(eventName, preventDefaults, false);
            document.body.addEventListener(eventName, preventDefaults, false);
        });

        // Mettre en surbrillance la zone de dépôt lorsqu'un élément est glissé dessus
        ['dragenter', 'dragover'].forEach(eventName => {
            dropArea.addEventListener(eventName, highlight, false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            dropArea.addEventListener(eventName, unhighlight, false);
        });

        // Gérer les fichiers déposés
        dropArea.addEventListener('drop', handleDrop, false);

        // Gérer les fichiers sélectionnés
        fileInput.addEventListener('change', handleFiles, false);

        function preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
        }

        function highlight() {
            dropArea.classList.add('highlight');
        }

        function unhighlight() {
            dropArea.classList.remove('highlight');
        }

        function handleDrop(e) {
            const dt = e.dataTransfer;
            const files = dt.files;
            handleFiles({ target: { files } });
        }

        async function handleFiles(e) {
            const files = e.target.files;

            // Vérifier si l'ajout de ces fichiers dépasserait la limite de 10 images
            const currentImageCount = previewContainer.children.length;
            if (currentImageCount + files.length > 10) {
                alert(`Vous avez déjà ${currentImageCount} images. Vous ne pouvez ajouter que ${10 - currentImageCount} de plus (maximum 10 au total).`);
                return;
            }

            // Traiter chaque fichier
            for (const file of [...files]) {
                if (!file.type.match('image.*')) {
                    alert('Veuillez télécharger uniquement des fichiers image.');
                    continue;
                }

                const reader = new FileReader();
                reader.onload = async function(e) {
                    const preview = document.createElement('div');
                    preview.className = 'preview-item';

                    const img = document.createElement('img');
                    img.src = e.target.result;

                    const removeBtn = document.createElement('button');
                    removeBtn.className = 'preview-remove';
                    removeBtn.innerHTML = '<i class="fas fa-times"></i>';
                    removeBtn.addEventListener('click', function() {
                        preview.remove();

                        // Mettre à jour le message d'erreur s'il n'y a pas d'images
                        document.getElementById('fileError').style.display =
                            previewContainer.querySelectorAll('.preview-item').length === 0 ? 'block' : 'none';
                    });

                    preview.appendChild(img);
                    preview.appendChild(removeBtn);
                    previewContainer.appendChild(preview);
                }
                reader.readAsDataURL(file);
            }

            // Afficher l'erreur s'il n'y a pas de fichiers
            document.getElementById('fileError').style.display = files.length === 0 ? 'block' : 'none';
        }

        // Validation du formulaire
        form.addEventListener('submit', async function(e) {
            e.preventDefault();

            let isValid = true;

            // Valider le nom
            const name = document.getElementById('name').value.trim();
            if (name === '') {
                document.getElementById('nameError').style.display = 'block';
                isValid = false;
            } else {
                document.getElementById('nameError').style.display = 'none';
            }

            // Valider le nom de famille
            const familyName = document.getElementById('familyName').value.trim();
            if (familyName === '') {
                document.getElementById('familyNameError').style.display = 'block';
                isValid = false;
            } else {
                document.getElementById('familyNameError').style.display = 'none';
            }

            // Valider la date de naissance
            const dob = document.getElementById('dob').value;
            if (dob === '') {
                document.getElementById('dobError').style.display = 'block';
                isValid = false;
            } else {
                document.getElementById('dobError').style.display = 'none';
            }

            // Valider les fichiers
            const previewItems = previewContainer.querySelectorAll('.preview-item');
            if (previewItems.length === 0) {
                document.getElementById('fileError').style.display = 'block';
                isValid = false;
            } else {
                document.getElementById('fileError').style.display = 'none';
            }

            if (!isValid) {
                return;
            }

            // Afficher la progression
            progressContainer.style.display = 'block';
            submitBtn.disabled = true;
            progressText.textContent = 'Préparation du téléchargement...';
            progressBar.style.width = '10%';

            // Créer l'objet FormData
            const formData = new FormData(form);

            // Utiliser XMLHttpRequest pour le suivi de la progression
            const xhr = new XMLHttpRequest();

            // Suivre la progression du téléchargement
            xhr.upload.addEventListener('progress', function(e) {
                if (e.lengthComputable) {
                    const percentComplete = Math.round((e.loaded / e.total) * 90) + 10;
                    progressBar.style.width = percentComplete + '%';
                    progressText.textContent = `Téléchargement... ${percentComplete}%`;
                }
            });

            // Gérer l'achèvement
            xhr.addEventListener('load', function() {
                if (xhr.status >= 200 && xhr.status < 300) {
                    progressBar.style.width = '100%';
                    progressText.textContent = 'Terminé!';

                    // Remplacer la page actuelle par la réponse
                    document.open();
                    document.write(xhr.responseText);
                    document.close();
                } else {
                    // Gérer l'erreur
                    submitBtn.disabled = false;
                    progressContainer.style.display = 'none';
                    alert('Échec du téléchargement. Veuillez réessayer.');
                }
            });

            // Gérer les erreurs
            xhr.addEventListener('error', function() {
                submitBtn.disabled = false;
                progressContainer.style.display = 'none';
                alert('Échec du téléchargement. Veuillez vérifier votre connexion et réessayer.');
            });

            // Ouvrir et envoyer la requête
            xhr.open('POST', form.action || window.location.href);
            xhr.send(formData);
        });
    </script>
</body>
</html>
'''

@app.route('/', methods=['GET', 'POST'])
def upload_file():
    if request.method == 'POST':
        print("=== ENROLLMENT DEBUG: Starting enrollment process ===")

        # Get form data
        name = request.form.get('name', '').strip()
        family_name = request.form.get('family_name', '').strip()
        dob = request.form.get('dob', '').strip()
        class_id = request.form.get('class_id', '').strip()

        print(f"DEBUG: Form data received - Name: '{name}', Family Name: '{family_name}', DOB: '{dob}', Class ID: '{class_id}'")

        # Validate required fields
        if not all([name, family_name, dob]):
            print("DEBUG: Validation failed - missing required fields")
            return "Informations requises manquantes", 400

        print("DEBUG: Form validation passed")

        # Create student folder
        student_id = f"{uuid.uuid4()}_{int(datetime.now().timestamp())}"
        print(f"DEBUG: Generated student ID: {student_id}")

        try:
            if class_id:
                class_folder = os.path.join(UPLOAD_BASE_FOLDER, f"class_{class_id}")
                os.makedirs(class_folder, exist_ok=True)
                student_folder = os.path.join(class_folder, student_id)
                print(f"DEBUG: Created class folder structure: {student_folder}")
            else:
                student_folder = os.path.join(UPLOAD_BASE_FOLDER, student_id)
                print(f"DEBUG: Created student folder: {student_folder}")

            os.makedirs(student_folder)
            print(f"DEBUG: Successfully created student directory: {student_folder}")
        except Exception as e:
            print(f"DEBUG ERROR: Failed to create student folder: {e}")
            return f"Erreur lors de la création du dossier étudiant: {str(e)}", 500

        # Save student info
        try:
            info_file_path = os.path.join(student_folder, "info.txt")
            with open(info_file_path, 'w') as f:
                f.write(f"Name: {name}\n")
                f.write(f"Family Name: {family_name}\n")
                f.write(f"Date of Birth: {dob}\n")
                f.write(f"Class ID: {class_id}\n")
                f.write(f"Upload Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            print(f"DEBUG: Successfully saved student info to: {info_file_path}")
        except Exception as e:
            print(f"DEBUG ERROR: Failed to save student info: {e}")
            return f"Erreur lors de la sauvegarde des informations: {str(e)}", 500

        # Handle file uploads
        if 'files[]' not in request.files:
            print("DEBUG ERROR: No files found in request")
            return "Aucun fichier téléchargé", 400

        files = request.files.getlist('files[]')
        print(f"DEBUG: Received {len(files)} files for upload")
        uploaded = []

        for i, file in enumerate(files):
            print(f"DEBUG: Processing file {i+1}/{len(files)}: '{file.filename}'")

            if file.filename == '' or not allowed_file(file.filename):
                print(f"DEBUG: Skipping invalid file: '{file.filename}'")
                continue

            try:
                # Save file with unique name
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
                filename = f"{timestamp}_{file.filename}"
                file_path = os.path.join(student_folder, filename)
                file.save(file_path)
                uploaded.append(filename)
                print(f"DEBUG: Successfully saved file: {filename} to {file_path}")

                # Check if file was actually saved and has content
                if os.path.exists(file_path):
                    file_size = os.path.getsize(file_path)
                    print(f"DEBUG: File saved with size: {file_size} bytes")
                else:
                    print(f"DEBUG ERROR: File was not saved properly: {file_path}")

            except Exception as e:
                print(f"DEBUG ERROR: Failed to save file '{file.filename}': {e}")
                continue

        if not uploaded:
            print("DEBUG ERROR: No valid images were uploaded")
            return "Aucune image valide téléchargée", 400

        print(f"DEBUG: Successfully uploaded {len(uploaded)} files: {uploaded}")

        # Process face encodings
        full_name = f"{name} {family_name}"
        print(f"DEBUG: Starting face processing for: '{full_name}'")
        print(f"DEBUG: Processing images from folder: {student_folder}")

        try:
            encodings = process_person_images(student_folder)
            print(f"DEBUG: Face processing completed. Found {len(encodings) if encodings else 0} valid face encodings")

            if encodings:
                print(f"DEBUG: Encoding details - Total encodings: {len(encodings)}")
                for i, enc in enumerate(encodings):
                    print(f"DEBUG: Encoding {i+1}: shape={len(enc) if enc is not None else 'None'}")
            else:
                print("DEBUG: No face encodings were generated")

        except Exception as e:
            print(f"DEBUG ERROR: Face processing failed: {e}")
            encodings = []

        # Add to database
        database_success = False
        database_error = None

        print(f"DEBUG: Starting database operations for '{full_name}'")

        if encodings and len(encodings) > 0:
            try:
                print(f"DEBUG: Attempting to add person to database with {len(encodings)} encodings")
                success = add_person_to_database(full_name, encodings, class_id if class_id else None)
                print(f"DEBUG: Database add operation result: {success}")

                if success:
                    database_success = True
                    print("DEBUG: Successfully added person to database")
                else:
                    database_error = "Échec de l'ajout à la base de données"
                    print("DEBUG: Database add operation returned False")

            except Exception as e:
                print(f"DEBUG ERROR: Database operation failed: {e}")
                print(f"DEBUG ERROR: Exception type: {type(e).__name__}")
                print(f"DEBUG ERROR: Exception args: {e.args}")

                if "UNIQUE constraint failed" in str(e):
                    database_error = "Le nom de l'étudiant existe déjà"
                    print("DEBUG: Detected duplicate name constraint violation")
                else:
                    database_error = f"Erreur de base de données: {str(e)}"
                    print(f"DEBUG: General database error: {str(e)}")
        else:
            database_error = "Aucune image de visage valide détectée"
            print("DEBUG: No valid face encodings found, skipping database operation")

        print(f"DEBUG: Final results - Database success: {database_success}, Error: {database_error}")
        print("=== ENROLLMENT DEBUG: Process completed ===")

        # Return success page
        return get_enrollment_success_html(student_id, class_id, uploaded, database_success, database_error)

    else:
        # GET request - show form
        class_id = request.args.get('class_id', '')
        class_name = request.args.get('class_name', 'Inscription Étudiant')
        
        if request.args.get('check') == '1':
            return "", 200

        # Customize form
        custom_form = HTML_FORM.replace('<h2>Formulaire d\'Inscription Étudiant</h2>', f'<h2>Inscription pour {class_name}</h2>')
        custom_form = custom_form.replace('<form method="POST"', f'<form method="POST"')
        
        if class_id:
            custom_form = custom_form.replace('<form method="POST" enctype="multipart/form-data">',
                                            f'<form method="POST" enctype="multipart/form-data">\n'
                                            f'<input type="hidden" name="class_id" value="{class_id}">')

        return render_template_string(custom_form)



def get_enrollment_success_html(student_id, class_id, uploaded, database_success, database_error):
    print(f"DEBUG: Generating success page - Student ID: {student_id}, Database Success: {database_success}")
    print(f"DEBUG: Database Error: {database_error}")
    print(f"DEBUG: Uploaded files: {uploaded}")

    status = "Succès!" if database_success else "Terminé avec des problèmes"
    color = "#28a745" if database_success else "#ffc107"

    error_section = ""
    if database_error:
        error_section = f'<div style="background: #fff3e0; padding: 15px; margin: 15px 0; border-radius: 8px; color: #e65100; border-left: 4px solid #ff9800;"><strong>Note:</strong> {database_error}</div>'
        print(f"DEBUG: Added error section to success page")

    return f'''
    <!doctype html>
    <html>
    <head>
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <title>Inscription Terminée</title>
        <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
        <style>
            body {{
                font-family: 'Poppins', sans-serif;
                background-color: #f5f7fa;
                margin: 0;
                padding: 20px;
                color: #333;
                display: flex;
                justify-content: center;
                align-items: center;
                min-height: 100vh;
            }}
            .success-card {{
                max-width: 600px;
                background: white;
                border-radius: 10px;
                box-shadow: 0 4px 20px rgba(0,0,0,0.1);
                padding: 40px 30px;
                text-align: center;
            }}
            h1 {{
                color: {color};
                margin-bottom: 20px;
                font-weight: 600;
            }}
            p {{
                color: #555;
                font-size: 16px;
                line-height: 1.6;
                margin-bottom: 15px;
            }}
            .success-icon {{
                width: 100px;
                height: 100px;
                background-color: #e8f5e9;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                margin: 0 auto 25px;
            }}
            .success-icon i {{
                font-size: 50px;
                color: #2ecc71;
            }}
            .info-item {{
                background-color: #f8f9fa;
                border-radius: 8px;
                padding: 15px;
                margin-bottom: 15px;
                text-align: left;
                border-left: 4px solid #3498db;
            }}
            .info-label {{
                font-weight: 600;
                color: #3498db;
                margin-bottom: 5px;
                font-size: 14px;
            }}
            .info-value {{
                font-size: 16px;
                color: #333;
            }}
            .btn {{
                background-color: #3498db;
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 5px;
                cursor: pointer;
                font-size: 16px;
                font-weight: 500;
                display: inline-flex;
                align-items: center;
                gap: 8px;
                text-decoration: none;
                transition: background-color 0.3s;
            }}
            .btn:hover {{
                background-color: #2980b9;
                color: white;
                text-decoration: none;
            }}
        </style>
    </head>
    <body>
        <div class="success-card">
            <div class="success-icon" style="{'' if database_success else 'background-color: #fff3e0;'}">
                <i class="fas {'fa-check' if database_success else 'fa-exclamation-triangle'}" style="{'' if database_success else 'color: #ff9800;'}"></i>
            </div>
            <h1>{status}</h1>
            <p>Vos informations ont été soumises avec succès{' et ajoutées à la base de données' if database_success else ''}.</p>

            {error_section}

            <div class="info-item">
                <div class="info-label">ID Étudiant</div>
                <div class="info-value">{student_id}</div>
            </div>

            {f'''
            <div class="info-item">
                <div class="info-label">Classe</div>
                <div class="info-value">{class_id}</div>
            </div>
            ''' if class_id else ''}

            <div class="info-item">
                <div class="info-label">Images téléchargées</div>
                <div class="info-value">{len(uploaded)} image{'' if len(uploaded) == 1 else 's'}</div>
            </div>

            <a href="/" class="btn">
                <i class="fas fa-arrow-left"></i> Retour au formulaire
            </a>
        </div>
    </body>
    </html>
    '''

def run_server(host=None, port=None):
    from facial_recognition_system.config import Config
    if host is None:
        host = Config.HOST
    if port is None:
        port = Config.ENROLLMENT_PORT

    print(f"DEBUG: Starting enrollment server on {host}:{port}")
    print(f"DEBUG: Upload folder: {UPLOAD_BASE_FOLDER}")
    print(f"DEBUG: Max content length: {app.config.get('MAX_CONTENT_LENGTH', 'Not set')}")

    import logging
    logging.getLogger('werkzeug').setLevel(logging.WARNING)

    try:
        app.run(host=host, port=port, threaded=True)
    except Exception as e:
        print(f"DEBUG ERROR: Failed to start enrollment server: {e}")

if __name__ == '__main__':
    run_server()
