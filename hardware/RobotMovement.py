"""
Robot Movement Controller for Teacher Assistant
Provides high-level movement functions for robot expressions and gestures
"""

import time
import threading
import random
from typing import Optional, Callable
from .ServoController import get_servo_controller, ServoController


class RobotMovement:
    """
    High-level robot movement controller for expressions and gestures
    """
    
    def __init__(self):
        self.servo_controller = get_servo_controller()
        self.animation_thread = None
        self.animation_running = False
        self.animation_lock = threading.Lock()
        
    def initialize(self) -> bool:
        """
        Initialize robot movement system
        
        Returns:
            bool: True if initialization successful
        """
        return self.servo_controller.initialize()
    
    # === Basic Movements ===
    
    def look_center(self, speed: float = 1.0) -> bool:
        """Look straight ahead (center position)"""
        print("DEBUG: Robot looking center")
        return self.servo_controller.move_neck(90, 90, speed)
    
    def look_left(self, angle: float = 45, speed: float = 1.0) -> bool:
        """Look to the left"""
        print(f"DEBUG: Robot looking left {angle}°")
        return self.servo_controller.move_neck(90 - angle, 90, speed)
    
    def look_right(self, angle: float = 45, speed: float = 1.0) -> bool:
        """Look to the right"""
        print(f"DEBUG: Robot looking right {angle}°")
        return self.servo_controller.move_neck(90 + angle, 90, speed)
    
    def look_up(self, angle: float = 30, speed: float = 1.0) -> bool:
        """Look up"""
        print(f"DEBUG: Robot looking up {angle}°")
        return self.servo_controller.move_neck(90, 90 + angle, speed)
    
    def look_down(self, angle: float = 30, speed: float = 1.0) -> bool:
        """Look down"""
        print(f"DEBUG: Robot looking down {angle}°")
        return self.servo_controller.move_neck(90, 90 - angle, speed)
    
    def hands_neutral(self, speed: float = 1.0) -> bool:
        """Move hands to neutral position"""
        print("DEBUG: Robot hands to neutral position")
        return self.servo_controller.move_hands(90, 90, speed)
    
    def hands_up(self, speed: float = 1.0) -> bool:
        """Raise both hands up"""
        print("DEBUG: Robot raising hands")
        return self.servo_controller.move_hands(45, 135, speed)
    
    def hands_down(self, speed: float = 1.0) -> bool:
        """Lower both hands down"""
        print("DEBUG: Robot lowering hands")
        return self.servo_controller.move_hands(135, 45, speed)
    
    def wave_hello(self, speed: float = 1.5) -> bool:
        """Wave hello with right hand"""
        print("DEBUG: Robot waving hello")
        try:
            # Raise right hand
            self.servo_controller.move_servo('right_hand', 45, speed)
            time.sleep(0.2)
            
            # Wave motion
            for _ in range(3):
                self.servo_controller.move_servo('right_hand', 30, speed * 2)
                time.sleep(0.3)
                self.servo_controller.move_servo('right_hand', 60, speed * 2)
                time.sleep(0.3)
            
            # Return to neutral
            self.servo_controller.move_servo('right_hand', 90, speed)
            return True
            
        except Exception as e:
            print(f"DEBUG ERROR: Wave hello failed: {e}")
            return False
    
    def point_left(self, speed: float = 1.0) -> bool:
        """Point to the left with left hand"""
        print("DEBUG: Robot pointing left")
        return self.servo_controller.move_hands(45, 90, speed) and self.look_left(30, speed)
    
    def point_right(self, speed: float = 1.0) -> bool:
        """Point to the right with right hand"""
        print("DEBUG: Robot pointing right")
        return self.servo_controller.move_hands(90, 135, speed) and self.look_right(30, speed)
    
    # === Expressions ===
    
    def express_greeting(self) -> bool:
        """Express greeting gesture"""
        print("DEBUG: Robot expressing greeting")
        try:
            self.look_center()
            time.sleep(0.5)
            self.wave_hello()
            time.sleep(0.5)
            self.hands_neutral()
            return True
        except Exception as e:
            print(f"DEBUG ERROR: Greeting expression failed: {e}")
            return False
    
    def express_attention(self) -> bool:
        """Express attention-getting gesture"""
        print("DEBUG: Robot expressing attention")
        try:
            self.hands_up()
            time.sleep(0.5)
            self.look_left(20)
            time.sleep(0.3)
            self.look_right(20)
            time.sleep(0.3)
            self.look_center()
            self.hands_neutral()
            return True
        except Exception as e:
            print(f"DEBUG ERROR: Attention expression failed: {e}")
            return False
    
    def express_thinking(self) -> bool:
        """Express thinking gesture"""
        print("DEBUG: Robot expressing thinking")
        try:
            self.look_up(20)
            self.servo_controller.move_servo('right_hand', 60, 1.0)  # Hand to chin area
            time.sleep(2.0)
            self.look_center()
            self.hands_neutral()
            return True
        except Exception as e:
            print(f"DEBUG ERROR: Thinking expression failed: {e}")
            return False
    
    def express_confusion(self) -> bool:
        """Express confusion gesture"""
        print("DEBUG: Robot expressing confusion")
        try:
            # Tilt head and look puzzled
            for _ in range(2):
                self.servo_controller.move_neck(75, 95)  # Slight tilt
                time.sleep(0.5)
                self.servo_controller.move_neck(105, 85)  # Other direction
                time.sleep(0.5)
            
            self.look_center()
            return True
        except Exception as e:
            print(f"DEBUG ERROR: Confusion expression failed: {e}")
            return False
    
    # === Animation System ===
    
    def start_idle_animation(self, callback: Optional[Callable] = None):
        """
        Start idle animation (subtle movements when not actively doing something)
        
        Args:
            callback: Optional callback function to call during animation
        """
        if self.animation_running:
            return
            
        self.animation_running = True
        self.animation_thread = threading.Thread(target=self._idle_animation_loop, args=(callback,), daemon=True)
        self.animation_thread.start()
        print("DEBUG: Started idle animation")
    
    def stop_idle_animation(self):
        """Stop idle animation"""
        self.animation_running = False
        if self.animation_thread and self.animation_thread.is_alive():
            self.animation_thread.join(timeout=2.0)
        print("DEBUG: Stopped idle animation")
    
    def _idle_animation_loop(self, callback: Optional[Callable] = None):
        """Internal idle animation loop"""
        while self.animation_running:
            try:
                with self.animation_lock:
                    if not self.animation_running:
                        break
                        
                    # Random subtle movements
                    movement_type = random.choice(['blink', 'slight_turn', 'micro_nod', 'rest'])
                    
                    if movement_type == 'slight_turn':
                        # Slight head turn
                        angle = random.uniform(-15, 15)
                        self.servo_controller.move_neck(90 + angle, 90, 0.5)
                        time.sleep(random.uniform(1.0, 3.0))
                        self.look_center(0.5)
                        
                    elif movement_type == 'micro_nod':
                        # Small nod
                        self.servo_controller.move_neck(90, 85, 0.8)
                        time.sleep(0.3)
                        self.servo_controller.move_neck(90, 95, 0.8)
                        time.sleep(0.3)
                        self.look_center(0.8)
                        
                    elif movement_type == 'blink':
                        # Simulate blink with slight hand movement
                        current_pos = self.servo_controller.current_positions.get('left_hand', 90)
                        self.servo_controller.move_servo('left_hand', current_pos + 5, 2.0)
                        time.sleep(0.1)
                        self.servo_controller.move_servo('left_hand', current_pos, 2.0)
                
                # Call callback if provided
                if callback:
                    try:
                        callback()
                    except Exception as e:
                        print(f"DEBUG ERROR: Animation callback failed: {e}")
                
                # Wait before next movement
                time.sleep(random.uniform(3.0, 8.0))
                
            except Exception as e:
                print(f"DEBUG ERROR: Idle animation error: {e}")
                time.sleep(1.0)
    
    # === Face Recognition Integration ===
    
    def on_face_detected(self, face_location: tuple, frame_size: tuple) -> bool:
        """
        React to face detection by looking at the detected face
        
        Args:
            face_location: (top, right, bottom, left) face coordinates
            frame_size: (height, width) of the frame
            
        Returns:
            bool: True if movement successful
        """
        try:
            top, right, bottom, left = face_location
            frame_height, frame_width = frame_size
            
            # Calculate face center
            face_center_x = (left + right) / 2
            face_center_y = (top + bottom) / 2
            
            # Convert to servo angles
            # Horizontal: 0 (left) to frame_width (right) -> 45° to 135°
            horizontal_angle = 45 + (face_center_x / frame_width) * 90
            
            # Vertical: 0 (top) to frame_height (bottom) -> 120° to 60° (inverted)
            vertical_angle = 120 - (face_center_y / frame_height) * 60
            
            print(f"DEBUG: Face detected at ({face_center_x}, {face_center_y}), moving to H:{horizontal_angle:.1f}° V:{vertical_angle:.1f}°")
            
            return self.servo_controller.move_neck(horizontal_angle, vertical_angle, 1.5)
            
        except Exception as e:
            print(f"DEBUG ERROR: Face tracking movement failed: {e}")
            return False
    
    def on_student_recognized(self, student_name: str) -> bool:
        """
        React to student recognition with greeting
        
        Args:
            student_name: Name of recognized student
            
        Returns:
            bool: True if movement successful
        """
        print(f"DEBUG: Student '{student_name}' recognized, performing greeting")
        return self.express_greeting()
    
    def on_no_face_detected(self) -> bool:
        """
        React when no face is detected (return to scanning)
        
        Returns:
            bool: True if movement successful
        """
        print("DEBUG: No face detected, returning to center")
        return self.look_center(0.8)
    
    def cleanup(self):
        """Clean up robot movement system"""
        self.stop_idle_animation()
        self.servo_controller.cleanup()


# Global robot movement instance
_robot_movement = None

def get_robot_movement() -> RobotMovement:
    """
    Get the global robot movement instance
    
    Returns:
        RobotMovement: The global robot movement controller
    """
    global _robot_movement
    if _robot_movement is None:
        _robot_movement = RobotMovement()
        _robot_movement.initialize()
    return _robot_movement

def cleanup_robot_movement():
    """Clean up the global robot movement controller"""
    global _robot_movement
    if _robot_movement is not None:
        _robot_movement.cleanup()
        _robot_movement = None
