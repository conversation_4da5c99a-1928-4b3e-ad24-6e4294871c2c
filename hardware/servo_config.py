"""
Servo Motor Configuration for Teacher Assistant Robot
Customize GPIO pin assignments and servo parameters here
"""

# GPIO Pin Assignments for Jetson Nano
# Modify these pins according to your hardware setup
SERVO_PINS = {
    'neck_horizontal': 18,  # GPIO 18 - Neck left/right movement
    'neck_vertical': 19,    # <PERSON><PERSON> 19 - Neck up/down movement  
    'left_hand': 20,        # GPIO 20 - Left hand movement
    'right_hand': 21        # GPIO 21 - Right hand movement
}

# Servo Angle Limits and Center Positions
# Adjust these values based on your servo specifications and mechanical limits
SERVO_LIMITS = {
    'neck_horizontal': {
        'min': 0,           # Minimum angle (degrees)
        'max': 180,         # Maximum angle (degrees)
        'center': 90,       # Center/neutral position (degrees)
        'name': 'Neck Horizontal',
        'description': 'Controls left/right head movement'
    },
    'neck_vertical': {
        'min': 30,          # Minimum angle (looking down limit)
        'max': 150,         # Maximum angle (looking up limit)
        'center': 90,       # Center/neutral position
        'name': 'Neck Vertical',
        'description': 'Controls up/down head movement'
    },
    'left_hand': {
        'min': 0,           # Minimum angle
        'max': 180,         # Maximum angle
        'center': 90,       # Center/neutral position
        'name': 'Left Hand',
        'description': 'Controls left hand/arm movement'
    },
    'right_hand': {
        'min': 0,           # Minimum angle
        'max': 180,         # Maximum angle
        'center': 90,       # Center/neutral position
        'name': 'Right Hand',
        'description': 'Controls right hand/arm movement'
    }
}

# PWM Configuration
PWM_FREQUENCY = 50  # Standard servo frequency (50Hz)

# Movement Speed Settings
MOVEMENT_SPEEDS = {
    'very_slow': 0.3,
    'slow': 0.5,
    'normal': 1.0,
    'fast': 1.5,
    'very_fast': 2.0
}

# Face Tracking Configuration
FACE_TRACKING = {
    'enabled': True,
    'sensitivity': 1.0,         # Movement sensitivity (0.1 = low, 2.0 = high)
    'smoothing': 0.8,           # Movement smoothing (0.1 = jerky, 0.9 = smooth)
    'timeout': 3.0,             # Seconds before returning to center when no face detected
    'min_face_size': 50,        # Minimum face size in pixels to track
    'tracking_zone': {          # Define tracking zone within frame
        'left': 0.1,            # 10% from left edge
        'right': 0.9,           # 90% from left edge (10% from right)
        'top': 0.1,             # 10% from top edge
        'bottom': 0.9           # 90% from top edge (10% from bottom)
    }
}

# Idle Animation Configuration
IDLE_ANIMATION = {
    'enabled': True,
    'min_interval': 3.0,        # Minimum seconds between movements
    'max_interval': 8.0,        # Maximum seconds between movements
    'movement_types': [         # Types of idle movements
        'slight_turn',          # Small head turns
        'micro_nod',            # Small nods
        'blink',                # Simulated blink
        'rest'                  # No movement
    ],
    'movement_weights': [       # Probability weights for each movement type
        0.3,                    # slight_turn: 30%
        0.2,                    # micro_nod: 20%
        0.1,                    # blink: 10%
        0.4                     # rest: 40%
    ]
}

# Expression Gesture Configurations
EXPRESSIONS = {
    'greeting': {
        'sequence': [
            ('look_center', 0.5),
            ('wave_hello', 2.0),
            ('hands_neutral', 0.5)
        ],
        'description': 'Welcome greeting for new students'
    },
    'attention': {
        'sequence': [
            ('hands_up', 0.5),
            ('look_left', 0.3, 20),
            ('look_right', 0.3, 20),
            ('look_center', 0.3),
            ('hands_neutral', 0.5)
        ],
        'description': 'Get attention gesture'
    },
    'thinking': {
        'sequence': [
            ('look_up', 0.5, 20),
            ('move_servo', 1.0, 'right_hand', 60),  # Hand to chin
            ('wait', 2.0),
            ('look_center', 0.5),
            ('hands_neutral', 0.5)
        ],
        'description': 'Thinking pose'
    },
    'confusion': {
        'sequence': [
            ('move_neck', 0.5, 75, 95),   # Slight tilt
            ('wait', 0.5),
            ('move_neck', 0.5, 105, 85),  # Other direction
            ('wait', 0.5),
            ('look_center', 0.5)
        ],
        'description': 'Confused head tilt'
    }
}

# Safety Configuration
SAFETY = {
    'max_speed': 2.0,           # Maximum movement speed multiplier
    'emergency_stop': True,     # Enable emergency stop functionality
    'soft_limits': True,        # Enable software angle limits
    'startup_check': True,      # Perform servo check on startup
    'timeout': 30.0,            # Maximum time for any single movement (seconds)
    'retry_attempts': 3         # Number of retry attempts for failed movements
}

# Debug Configuration
DEBUG = {
    'verbose_logging': True,    # Enable detailed debug output
    'log_movements': True,      # Log all servo movements
    'log_face_tracking': True,  # Log face tracking data
    'mock_mode': False          # Use mock servos for testing (auto-detected)
}

def validate_config():
    """
    Validate the servo configuration
    
    Returns:
        tuple: (is_valid, error_messages)
    """
    errors = []
    
    # Check that all required servos have pin assignments
    required_servos = ['neck_horizontal', 'neck_vertical', 'left_hand', 'right_hand']
    for servo in required_servos:
        if servo not in SERVO_PINS:
            errors.append(f"Missing pin assignment for servo: {servo}")
        if servo not in SERVO_LIMITS:
            errors.append(f"Missing limits configuration for servo: {servo}")
    
    # Check for duplicate pin assignments
    pins = list(SERVO_PINS.values())
    if len(pins) != len(set(pins)):
        errors.append("Duplicate GPIO pin assignments detected")
    
    # Validate servo limits
    for servo, limits in SERVO_LIMITS.items():
        if limits['min'] >= limits['max']:
            errors.append(f"Invalid angle limits for {servo}: min >= max")
        if not (limits['min'] <= limits['center'] <= limits['max']):
            errors.append(f"Center position for {servo} is outside min/max limits")
    
    # Validate GPIO pin numbers (Jetson Nano valid pins)
    valid_pins = [7, 11, 12, 13, 15, 16, 18, 19, 21, 22, 23, 24, 26, 29, 31, 32, 33, 35, 36, 37, 38, 40]
    for servo, pin in SERVO_PINS.items():
        if pin not in valid_pins:
            errors.append(f"Invalid GPIO pin {pin} for {servo}. Valid pins: {valid_pins}")
    
    return len(errors) == 0, errors

def print_config_summary():
    """Print a summary of the current configuration"""
    print("🤖 Robot Servo Configuration Summary")
    print("=" * 50)
    
    print("\n📍 GPIO Pin Assignments:")
    for servo, pin in SERVO_PINS.items():
        name = SERVO_LIMITS[servo]['name']
        print(f"  {name:20} -> GPIO {pin}")
    
    print("\n⚙️ Servo Limits:")
    for servo, limits in SERVO_LIMITS.items():
        print(f"  {limits['name']:20} -> {limits['min']:3}° to {limits['max']:3}° (center: {limits['center']:3}°)")
    
    print(f"\n🔧 PWM Frequency: {PWM_FREQUENCY}Hz")
    print(f"👁️ Face Tracking: {'Enabled' if FACE_TRACKING['enabled'] else 'Disabled'}")
    print(f"💤 Idle Animation: {'Enabled' if IDLE_ANIMATION['enabled'] else 'Disabled'}")
    
    # Validate configuration
    is_valid, errors = validate_config()
    if is_valid:
        print("\n✅ Configuration is valid")
    else:
        print("\n❌ Configuration errors:")
        for error in errors:
            print(f"  - {error}")

if __name__ == "__main__":
    print_config_summary()
