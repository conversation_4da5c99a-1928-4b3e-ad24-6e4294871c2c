"""
Hardware module for Teacher Assistant

This module contains camera and hardware-related components:
- JetsonCamera: CSI camera interface for Jetson Nano
- Focuser: Camera focus control for Arducam lenses
- ServoController: Servo motor control for robot movements
- RobotMovement: High-level robot movement and expressions
- camera_diagnostic: Diagnostic tools for camera troubleshooting
"""

# Import real camera components
from .JetsonCamera import Camera as JetsonCamera
from .Focuser import Focuser

# Import servo and movement components
from .ServoController import ServoController, get_servo_controller, cleanup_servo_controller
from .RobotMovement import RobotMovement, get_robot_movement, cleanup_robot_movement

# Always use the real Jetson camera
Camera = JetsonCamera

__all__ = [
    'Camera', 'JetsonCamera', 'Focuser',
    'ServoController', 'get_servo_controller', 'cleanup_servo_controller',
    'RobotMovement', 'get_robot_movement', 'cleanup_robot_movement'
]
