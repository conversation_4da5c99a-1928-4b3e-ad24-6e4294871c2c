"""
Servo Motor Controller for Teacher Assistant Robot
Handles neck and hand movements using PWM on Jetson Nano
"""

import time
import threading
from typing import Dict, <PERSON><PERSON>, Optional
from .servo_config import SERVO_PINS, SERVO_LIMITS, PWM_FREQUENCY, SAFETY

try:
    # Try to import Jetson.GPIO for Jetson Nano
    import Jetson.GPIO as GPIO
    JETSON_AVAILABLE = True
    print("DEBUG: Jetson.GPIO imported successfully")
except ImportError:
    try:
        # Fallback to RPi.GPIO for Raspberry Pi compatibility
        import RPi.GPIO as GPIO
        JETSON_AVAILABLE = True
        print("DEBUG: RPi.GPIO imported as fallback")
    except ImportError:
        # Mock GPIO for development environments
        print("DEBUG: No GPIO library available, using mock GPIO")
        JETSON_AVAILABLE = False
        
        class MockGPIO:
            BCM = "BCM"
            OUT = "OUT"
            
            @staticmethod
            def setmode(mode): pass
            @staticmethod
            def setup(pin, mode): pass
            @staticmethod
            def PWM(pin, freq): return MockPWM()
            @staticmethod
            def cleanup(): pass
            
        class MockPWM:
            def start(self, duty): pass
            def ChangeDutyCycle(self, duty): pass
            def stop(self): pass
            
        GPIO = MockGPIO()


class ServoController:
    """
    Controls servo motors for robot neck and hand movements
    """
    
    # Use configuration from servo_config.py
    DEFAULT_PINS = SERVO_PINS
    SERVO_CONFIG = SERVO_LIMITS
    
    def __init__(self, pins: Optional[Dict[str, int]] = None):
        """
        Initialize servo controller
        
        Args:
            pins: Dictionary mapping servo names to GPIO pin numbers
        """
        self.pins = pins or self.DEFAULT_PINS
        self.pwm_objects = {}
        self.current_positions = {}
        self.movement_lock = threading.Lock()
        self.initialized = False
        
        print(f"DEBUG: ServoController initializing with pins: {self.pins}")
        
    def initialize(self) -> bool:
        """
        Initialize GPIO and PWM for all servos
        
        Returns:
            bool: True if initialization successful
        """
        try:
            if not JETSON_AVAILABLE:
                print("DEBUG: GPIO not available, servo controller in mock mode")
                self.initialized = True
                return True
                
            # Set GPIO mode
            GPIO.setmode(GPIO.BCM)
            print("DEBUG: GPIO mode set to BCM")
            
            # Initialize each servo
            for servo_name, pin in self.pins.items():
                try:
                    # Setup GPIO pin
                    GPIO.setup(pin, GPIO.OUT)
                    
                    # Create PWM object using configured frequency
                    pwm = GPIO.PWM(pin, PWM_FREQUENCY)
                    pwm.start(0)  # Start with 0% duty cycle
                    
                    self.pwm_objects[servo_name] = pwm
                    
                    # Set to center position
                    center_angle = self.SERVO_CONFIG[servo_name]['center']
                    self.current_positions[servo_name] = center_angle
                    
                    print(f"DEBUG: Initialized {self.SERVO_CONFIG[servo_name]['name']} on pin {pin}")
                    
                except Exception as e:
                    print(f"DEBUG ERROR: Failed to initialize {servo_name} on pin {pin}: {e}")
                    return False
            
            # Move all servos to center position
            self.move_to_center_position()
            
            self.initialized = True
            print("DEBUG: ServoController initialization completed successfully")
            return True
            
        except Exception as e:
            print(f"DEBUG ERROR: ServoController initialization failed: {e}")
            return False
    
    def angle_to_duty_cycle(self, angle: float) -> float:
        """
        Convert angle (0-180) to PWM duty cycle (2-12%)
        
        Args:
            angle: Servo angle in degrees (0-180)
            
        Returns:
            float: PWM duty cycle percentage
        """
        # Standard servo: 1ms = 0°, 1.5ms = 90°, 2ms = 180°
        # At 50Hz: 1ms = 5% duty, 1.5ms = 7.5% duty, 2ms = 10% duty
        duty_cycle = 2.5 + (angle / 180.0) * 10.0
        return max(2.0, min(12.0, duty_cycle))  # Clamp between 2-12%
    
    def move_servo(self, servo_name: str, angle: float, speed: float = 1.0) -> bool:
        """
        Move a specific servo to target angle
        
        Args:
            servo_name: Name of the servo ('neck_horizontal', 'neck_vertical', 'left_hand', 'right_hand')
            angle: Target angle in degrees
            speed: Movement speed (0.1 = slow, 1.0 = normal, 2.0 = fast)
            
        Returns:
            bool: True if movement successful
        """
        if not self.initialized:
            print("DEBUG ERROR: ServoController not initialized")
            return False
            
        if servo_name not in self.pins:
            print(f"DEBUG ERROR: Unknown servo: {servo_name}")
            return False
            
        # Validate angle limits
        config = self.SERVO_CONFIG[servo_name]
        angle = max(config['min'], min(config['max'], angle))
        
        try:
            with self.movement_lock:
                current_angle = self.current_positions.get(servo_name, config['center'])
                
                if not JETSON_AVAILABLE:
                    # Mock movement for development
                    print(f"DEBUG: Mock movement - {config['name']}: {current_angle}° → {angle}°")
                    self.current_positions[servo_name] = angle
                    time.sleep(0.1)  # Simulate movement time
                    return True
                
                # Calculate movement steps for smooth motion
                angle_diff = angle - current_angle
                steps = max(1, int(abs(angle_diff) / (5 * speed)))  # 5 degrees per step at normal speed
                step_size = angle_diff / steps
                step_delay = 0.02 / speed  # 20ms delay at normal speed
                
                print(f"DEBUG: Moving {config['name']}: {current_angle}° → {angle}° in {steps} steps")
                
                # Perform smooth movement
                pwm = self.pwm_objects[servo_name]
                for i in range(steps + 1):
                    intermediate_angle = current_angle + (step_size * i)
                    duty_cycle = self.angle_to_duty_cycle(intermediate_angle)
                    pwm.ChangeDutyCycle(duty_cycle)
                    time.sleep(step_delay)
                
                # Update current position
                self.current_positions[servo_name] = angle
                print(f"DEBUG: {config['name']} moved to {angle}°")
                return True
                
        except Exception as e:
            print(f"DEBUG ERROR: Failed to move {servo_name}: {e}")
            return False
    
    def move_to_center_position(self) -> bool:
        """
        Move all servos to their center positions
        
        Returns:
            bool: True if all movements successful
        """
        print("DEBUG: Moving all servos to center position")
        success = True
        
        for servo_name in self.pins.keys():
            center_angle = self.SERVO_CONFIG[servo_name]['center']
            if not self.move_servo(servo_name, center_angle):
                success = False
                
        return success
    
    def move_neck(self, horizontal_angle: float, vertical_angle: float, speed: float = 1.0) -> bool:
        """
        Move neck to specific position
        
        Args:
            horizontal_angle: Horizontal angle (0=left, 90=center, 180=right)
            vertical_angle: Vertical angle (30=down, 90=center, 150=up)
            speed: Movement speed
            
        Returns:
            bool: True if movement successful
        """
        print(f"DEBUG: Moving neck to H:{horizontal_angle}° V:{vertical_angle}°")
        
        h_success = self.move_servo('neck_horizontal', horizontal_angle, speed)
        v_success = self.move_servo('neck_vertical', vertical_angle, speed)
        
        return h_success and v_success
    
    def move_hands(self, left_angle: float, right_angle: float, speed: float = 1.0) -> bool:
        """
        Move both hands to specific positions
        
        Args:
            left_angle: Left hand angle
            right_angle: Right hand angle
            speed: Movement speed
            
        Returns:
            bool: True if movement successful
        """
        print(f"DEBUG: Moving hands - Left:{left_angle}° Right:{right_angle}°")
        
        left_success = self.move_servo('left_hand', left_angle, speed)
        right_success = self.move_servo('right_hand', right_angle, speed)
        
        return left_success and right_success
    
    def get_current_positions(self) -> Dict[str, float]:
        """
        Get current positions of all servos
        
        Returns:
            Dict mapping servo names to current angles
        """
        return self.current_positions.copy()
    
    def cleanup(self):
        """
        Clean up GPIO resources
        """
        try:
            if JETSON_AVAILABLE and self.initialized:
                # Stop all PWM
                for pwm in self.pwm_objects.values():
                    pwm.stop()
                
                # Clean up GPIO
                GPIO.cleanup()
                print("DEBUG: ServoController cleanup completed")
                
            self.initialized = False
            self.pwm_objects.clear()
            
        except Exception as e:
            print(f"DEBUG ERROR: ServoController cleanup failed: {e}")


# Global servo controller instance
_servo_controller = None

def get_servo_controller() -> ServoController:
    """
    Get the global servo controller instance
    
    Returns:
        ServoController: The global servo controller
    """
    global _servo_controller
    if _servo_controller is None:
        _servo_controller = ServoController()
        _servo_controller.initialize()
    return _servo_controller

def cleanup_servo_controller():
    """
    Clean up the global servo controller
    """
    global _servo_controller
    if _servo_controller is not None:
        _servo_controller.cleanup()
        _servo_controller = None
