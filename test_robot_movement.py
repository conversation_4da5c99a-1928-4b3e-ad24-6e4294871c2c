#!/usr/bin/env python3
"""
Test script for robot movement system
Tests servo motors and movement functions
"""

import time
import sys
from hardware.RobotMovement import get_robot_movement

def test_basic_movements():
    """Test basic robot movements"""
    print("🤖 Testing basic robot movements...")
    
    robot = get_robot_movement()
    
    if not robot.initialize():
        print("❌ Failed to initialize robot movement system")
        return False
    
    print("✅ Robot movement system initialized")
    
    try:
        # Test center position
        print("📍 Moving to center position...")
        robot.look_center()
        robot.hands_neutral()
        time.sleep(2)
        
        # Test neck movements
        print("👀 Testing neck movements...")
        robot.look_left(30)
        time.sleep(1)
        robot.look_right(30)
        time.sleep(1)
        robot.look_up(20)
        time.sleep(1)
        robot.look_down(20)
        time.sleep(1)
        robot.look_center()
        time.sleep(1)
        
        # Test hand movements
        print("👋 Testing hand movements...")
        robot.hands_up()
        time.sleep(1)
        robot.hands_down()
        time.sleep(1)
        robot.hands_neutral()
        time.sleep(1)
        
        # Test wave
        print("👋 Testing wave gesture...")
        robot.wave_hello()
        time.sleep(2)
        
        # Test expressions
        print("😊 Testing expressions...")
        robot.express_greeting()
        time.sleep(2)
        
        robot.express_attention()
        time.sleep(2)
        
        robot.express_thinking()
        time.sleep(2)
        
        # Return to neutral
        print("📍 Returning to neutral position...")
        robot.look_center()
        robot.hands_neutral()
        
        print("✅ All basic movements completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error during movement test: {e}")
        return False
    
    finally:
        robot.cleanup()

def test_face_tracking_simulation():
    """Test face tracking simulation"""
    print("👁️ Testing face tracking simulation...")
    
    robot = get_robot_movement()
    
    if not robot.initialize():
        print("❌ Failed to initialize robot movement system")
        return False
    
    try:
        # Simulate face detection at different positions
        frame_size = (480, 640)  # height, width
        
        # Face on the left
        print("👁️ Simulating face on the left...")
        face_location = (100, 200, 200, 100)  # top, right, bottom, left
        robot.on_face_detected(face_location, frame_size)
        time.sleep(2)
        
        # Face on the right
        print("👁️ Simulating face on the right...")
        face_location = (100, 540, 200, 440)
        robot.on_face_detected(face_location, frame_size)
        time.sleep(2)
        
        # Face in center
        print("👁️ Simulating face in center...")
        face_location = (150, 370, 250, 270)
        robot.on_face_detected(face_location, frame_size)
        time.sleep(2)
        
        # Simulate student recognition
        print("🎓 Simulating student recognition...")
        robot.on_student_recognized("Test Student")
        time.sleep(3)
        
        # Simulate no face detected
        print("❌ Simulating no face detected...")
        robot.on_no_face_detected()
        time.sleep(2)
        
        print("✅ Face tracking simulation completed!")
        return True
        
    except Exception as e:
        print(f"❌ Error during face tracking test: {e}")
        return False
    
    finally:
        robot.cleanup()

def test_idle_animation():
    """Test idle animation"""
    print("💤 Testing idle animation...")
    
    robot = get_robot_movement()
    
    if not robot.initialize():
        print("❌ Failed to initialize robot movement system")
        return False
    
    try:
        print("▶️ Starting idle animation for 10 seconds...")
        robot.start_idle_animation()
        time.sleep(10)
        
        print("⏹️ Stopping idle animation...")
        robot.stop_idle_animation()
        
        # Return to neutral
        robot.look_center()
        robot.hands_neutral()
        
        print("✅ Idle animation test completed!")
        return True
        
    except Exception as e:
        print(f"❌ Error during idle animation test: {e}")
        return False
    
    finally:
        robot.cleanup()

def main():
    """Main test function"""
    print("🤖 Robot Movement Test Suite")
    print("=" * 50)
    
    tests = [
        ("Basic Movements", test_basic_movements),
        ("Face Tracking Simulation", test_face_tracking_simulation),
        ("Idle Animation", test_idle_animation)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 Running test: {test_name}")
        print("-" * 30)
        
        try:
            if test_func():
                print(f"✅ {test_name} PASSED")
                passed += 1
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} FAILED with exception: {e}")
        
        # Wait between tests
        if test_name != tests[-1][0]:  # Not the last test
            print("\n⏳ Waiting 3 seconds before next test...")
            time.sleep(3)
    
    print("\n" + "=" * 50)
    print(f"🏁 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Robot movement system is working correctly.")
        return 0
    else:
        print("⚠️ Some tests failed. Check the output above for details.")
        return 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n\n⏹️ Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ Unexpected error: {e}")
        sys.exit(1)
