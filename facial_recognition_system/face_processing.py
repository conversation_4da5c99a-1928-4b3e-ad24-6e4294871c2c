import face_recognition
import numpy as np
import os
import cv2
import time
from concurrent.futures import ProcessPoolExecutor
from facial_recognition_system.config import Config

def process_single_image(image_path):
    print(f"DEBUG: Processing single image: {image_path}")
    try:
        image = cv2.imread(image_path)
        if image is None:
            print(f"DEBUG ERROR: Could not read image: {image_path}")
            return None

        print(f"DEBUG: Image loaded, original size: {image.shape}")
        small_image = cv2.resize(image, (0, 0), fx=Config.IMAGE_SCALE_FACTOR, fy=Config.IMAGE_SCALE_FACTOR)
        print(f"DEBUG: Image resized to: {small_image.shape}")

        rgb_image = cv2.cvtColor(small_image, cv2.COLOR_BGR2RGB)
        face_locations = face_recognition.face_locations(rgb_image, model=Config.FACE_MODEL)
        print(f"DEBUG: Found {len(face_locations)} faces in {image_path}")

        if len(face_locations) != 1:
            print(f"DEBUG: Skipping image - expected 1 face, found {len(face_locations)}")
            return None

        face_enc = face_recognition.face_encodings(rgb_image, face_locations)[0]
        print(f"DEBUG: Successfully generated face encoding for {image_path}")
        return face_enc
    except Exception as e:
        print(f"DEBUG ERROR: Exception processing {image_path}: {e}")
        return None

def process_person_images(person_dir):
    print(f"DEBUG: Processing person images from directory: {person_dir}")
    encodings = []

    try:
        if not os.path.exists(person_dir):
            print(f"DEBUG ERROR: Directory does not exist: {person_dir}")
            return encodings

        all_files = os.listdir(person_dir)
        print(f"DEBUG: Found {len(all_files)} total files in directory: {all_files}")

        image_files = [f for f in all_files if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
        print(f"DEBUG: Found {len(image_files)} image files: {image_files}")

        if not image_files:
            print("DEBUG ERROR: No image files found in directory")
            return encodings

        with ProcessPoolExecutor(max_workers=Config.NUM_WORKERS) as executor:
            futures = [executor.submit(process_single_image, os.path.join(person_dir, image_file))
                      for image_file in image_files]

            for i, future in enumerate(futures):
                try:
                    result = future.result()
                    if result is not None:
                        encodings.append(result)
                        print(f"DEBUG: Successfully processed image {i+1}/{len(image_files)}")
                    else:
                        print(f"DEBUG: Failed to process image {i+1}/{len(image_files)}")
                except Exception as e:
                    print(f"DEBUG ERROR: Exception processing image {i+1}: {e}")

        print(f"DEBUG: Final result - {len(encodings)} valid encodings out of {len(image_files)} images")
        return encodings

    except Exception as e:
        print(f"DEBUG ERROR: Exception in process_person_images: {e}")
        return encodings

def process_test_image(image_path, known_names, known_encodings):
    recognized = []
    try:
        image = cv2.imread(image_path)
        if image is None:
            return recognized
        small_image = cv2.resize(image, (0, 0), fx=Config.IMAGE_SCALE_FACTOR, fy=Config.IMAGE_SCALE_FACTOR)
        rgb_image = cv2.cvtColor(small_image, cv2.COLOR_BGR2RGB)
        face_locations = face_recognition.face_locations(rgb_image, model='hog')
        if not face_locations:
            return recognized
        face_encodings = face_recognition.face_encodings(rgb_image, face_locations)
        for face_enc in face_encodings:
            face_distances = face_recognition.face_distance(known_encodings, face_enc)
            best_match_index = np.argmin(face_distances)
            if face_distances[best_match_index] <= Config.TOLERANCE:
                name = known_names[best_match_index]
                recognized.append(name)
    except Exception:
        pass
    return recognized

def batch_process_test_images(test_files, known_names, known_encodings):
    attendance = {name: "Absent" for name in known_names}
    with ProcessPoolExecutor(max_workers=Config.NUM_WORKERS) as executor:
        futures = {executor.submit(process_test_image, path, known_names, known_encodings): path
                  for path in test_files}
        total = len(futures)
        processed = 0
        bar_length = 40
        print("Progress: ", end="")
        for future in futures:
            recognized_names = future.result()
            for name in recognized_names:
                attendance[name] = "Present"
            processed += 1
            percent = processed / total
            arrow = '=' * int(round(percent * bar_length) - 1) + '>'
            spaces = ' ' * (bar_length - len(arrow))
            print(f"\rProgress: [{arrow + spaces}] {int(percent * 100)}%", end="")
            time.sleep(0.01)
        print("\rProgress: [" + '=' * bar_length + "] 100%")
    return attendance
