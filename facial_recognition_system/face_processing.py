import face_recognition
import numpy as np
import os
import cv2
import time
from concurrent.futures import <PERSON><PERSON><PERSON>Executor
from facial_recognition_system.config import Config

# Force CPU-only mode to avoid CUDA issues
os.environ['CUDA_VISIBLE_DEVICES'] = ''
os.environ['DLIB_USE_CUDA'] = '0'

def process_single_image(image_path):
    print(f"DEBUG: Processing single image: {image_path}")
    try:
        image = cv2.imread(image_path)
        if image is None:
            print(f"DEBUG ERROR: Could not read image: {image_path}")
            return None

        print(f"DEBUG: Image loaded, original size: {image.shape}")
        small_image = cv2.resize(image, (0, 0), fx=Config.IMAGE_SCALE_FACTOR, fy=Config.IMAGE_SCALE_FACTOR)
        print(f"DEBUG: Image resized to: {small_image.shape}")

        rgb_image = cv2.cvtColor(small_image, cv2.COLOR_BGR2RGB)

        # Try face detection with error handling for CUDA issues
        try:
            face_locations = face_recognition.face_locations(rgb_image, model='hog')
            print(f"DEBUG: Found {len(face_locations)} faces using HOG model")
        except Exception as cuda_error:
            if "cuda" in str(cuda_error).lower():
                print(f"DEBUG: CUDA error detected, retrying with CPU-only mode: {cuda_error}")
                # Force CPU-only processing
                face_locations = face_recognition.face_locations(rgb_image, model='hog', number_of_times_to_upsample=1)
                print(f"DEBUG: Found {len(face_locations)} faces using CPU-only HOG model")
            else:
                raise cuda_error

        if len(face_locations) != 1:
            print(f"DEBUG: Skipping image - expected 1 face, found {len(face_locations)}")
            return None

        # Try face encoding with error handling
        try:
            face_enc = face_recognition.face_encodings(rgb_image, face_locations)[0]
            print(f"DEBUG: Successfully generated face encoding for {image_path}")
            return face_enc
        except Exception as encoding_error:
            if "cuda" in str(encoding_error).lower():
                print(f"DEBUG: CUDA error in encoding, retrying: {encoding_error}")
                # Try with smaller image or different parameters
                smaller_rgb = cv2.resize(rgb_image, (0, 0), fx=0.5, fy=0.5)
                smaller_locations = [(int(top*0.5), int(right*0.5), int(bottom*0.5), int(left*0.5))
                                   for top, right, bottom, left in face_locations]
                face_enc = face_recognition.face_encodings(smaller_rgb, smaller_locations)[0]
                print(f"DEBUG: Successfully generated face encoding with smaller image")
                return face_enc
            else:
                raise encoding_error

    except Exception as e:
        print(f"DEBUG ERROR: Exception processing {image_path}: {e}")
        print(f"DEBUG ERROR: Exception type: {type(e).__name__}")
        return None

def process_person_images(person_dir):
    print(f"DEBUG: Processing person images from directory: {person_dir}")
    encodings = []

    try:
        if not os.path.exists(person_dir):
            print(f"DEBUG ERROR: Directory does not exist: {person_dir}")
            return encodings

        all_files = os.listdir(person_dir)
        print(f"DEBUG: Found {len(all_files)} total files in directory: {all_files}")

        image_files = [f for f in all_files if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
        print(f"DEBUG: Found {len(image_files)} image files: {image_files}")

        if not image_files:
            print("DEBUG ERROR: No image files found in directory")
            return encodings

        # Try multiprocessing first, fall back to sequential processing if it fails
        try:
            print("DEBUG: Attempting multiprocessing approach...")
            with ProcessPoolExecutor(max_workers=min(Config.NUM_WORKERS, len(image_files))) as executor:
                futures = [executor.submit(process_single_image, os.path.join(person_dir, image_file))
                          for image_file in image_files]

                for i, future in enumerate(futures):
                    try:
                        result = future.result(timeout=30)  # Add timeout
                        if result is not None:
                            encodings.append(result)
                            print(f"DEBUG: Successfully processed image {i+1}/{len(image_files)}")
                        else:
                            print(f"DEBUG: Failed to process image {i+1}/{len(image_files)}")
                    except Exception as e:
                        print(f"DEBUG ERROR: Exception processing image {i+1}: {e}")

        except Exception as mp_error:
            print(f"DEBUG: Multiprocessing failed, falling back to sequential processing: {mp_error}")
            encodings = []  # Reset encodings

            # Sequential processing fallback
            for i, image_file in enumerate(image_files):
                try:
                    image_path = os.path.join(person_dir, image_file)
                    result = process_single_image(image_path)
                    if result is not None:
                        encodings.append(result)
                        print(f"DEBUG: Successfully processed image {i+1}/{len(image_files)} (sequential)")
                    else:
                        print(f"DEBUG: Failed to process image {i+1}/{len(image_files)} (sequential)")
                except Exception as e:
                    print(f"DEBUG ERROR: Exception processing image {i+1} (sequential): {e}")

        print(f"DEBUG: Final result - {len(encodings)} valid encodings out of {len(image_files)} images")
        return encodings

    except Exception as e:
        print(f"DEBUG ERROR: Exception in process_person_images: {e}")
        return encodings

def process_test_image(image_path, known_names, known_encodings):
    recognized = []
    try:
        image = cv2.imread(image_path)
        if image is None:
            return recognized
        small_image = cv2.resize(image, (0, 0), fx=Config.IMAGE_SCALE_FACTOR, fy=Config.IMAGE_SCALE_FACTOR)
        rgb_image = cv2.cvtColor(small_image, cv2.COLOR_BGR2RGB)
        face_locations = face_recognition.face_locations(rgb_image, model='hog')
        if not face_locations:
            return recognized
        face_encodings = face_recognition.face_encodings(rgb_image, face_locations)
        for face_enc in face_encodings:
            face_distances = face_recognition.face_distance(known_encodings, face_enc)
            best_match_index = np.argmin(face_distances)
            if face_distances[best_match_index] <= Config.TOLERANCE:
                name = known_names[best_match_index]
                recognized.append(name)
    except Exception:
        pass
    return recognized

def batch_process_test_images(test_files, known_names, known_encodings):
    attendance = {name: "Absent" for name in known_names}
    with ProcessPoolExecutor(max_workers=Config.NUM_WORKERS) as executor:
        futures = {executor.submit(process_test_image, path, known_names, known_encodings): path
                  for path in test_files}
        total = len(futures)
        processed = 0
        bar_length = 40
        print("Progress: ", end="")
        for future in futures:
            recognized_names = future.result()
            for name in recognized_names:
                attendance[name] = "Present"
            processed += 1
            percent = processed / total
            arrow = '=' * int(round(percent * bar_length) - 1) + '>'
            spaces = ' ' * (bar_length - len(arrow))
            print(f"\rProgress: [{arrow + spaces}] {int(percent * 100)}%", end="")
            time.sleep(0.01)
        print("\rProgress: [" + '=' * bar_length + "] 100%")
    return attendance
